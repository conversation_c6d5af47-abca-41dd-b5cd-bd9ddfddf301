const Http = require("./http-client")
const { MessageResolver } = require('../../utils/helper')

class Menu {
  static getInstance() {
    if (!this.instance) 
      this.instance = new Menu();
    return this.instance;
  }

  async findById(id) {
    //let response = await Http.get('/menu/' + id);
    //return response.data;
    return {
        id: id,
        name: "Comercial",
        href: null
      };
  }

  async findFuncByName(name, token){
    try{
      const response = await Http.get(`function/${name}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })

      return response.data
    }catch(e){
      return MessageResolver(e)
    }
  }

  async findByUserId(userId, token) {
    try{
      const response = await Http.get(`menu/${userId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      return response.data
    }catch(e){
      return MessageResolver(e)
    }
  }

  async findFavoriteByUserId(userId, token) {
    try{
      const response = await Http.get(`/favorite/${userId}`, {
        headers: { Authorization: `Bearer ${token}`}
      })

      return response.data
    }catch(e){
      return MessageResolver(e)
    }
  }
}

module.exports = Menu;