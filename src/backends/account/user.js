const Http = require("./http-client")

class Brand {

  static getInstance() {
    if (!this.instance) 
      this.instance = new Brand();
    return this.instance;
  }

  async login({ usuario, senha }) {
    try{
      const response = await Http.post('/login', {
        usuario,
        senha
      })
      
      return response
    }catch(e){
      return e
    }
  }

  async recovery(email, usuario) {
    try{
      let params = {email, usuario}
      const response = await Http.get('/recovery', {params})
      
      return response
    }catch(e){
      return e
    }
  }
  
  async recoveryChange(id, senha, token){
    try{
      const response = await Http.post('/recovery', {
        usuarioId: id,
        senha
      }, { headers: { Authorization: `Bearer ${token}` } })
      return response
    }catch(e){
      return e
    }
  }

  async changePicture(id, picture, token){
    try{
      const response = await Http.post(`/user/${id}/picture`, picture, {
        headers: { Authorization: `Bearer ${token}` }
      })

      return response
    }catch(e){
      return e
    }
  }

  async findById(id, token) {
    try{
      const response = await Http.get(`/user/${id}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      return response.data
    }catch(e){
      return e
    }
  }

  async findNotificationById(id, token){
    try{
      const response = await Http.get(`/user/${id}/notifications`, {
        headers: { Authorization: `Bearer ${token}` }
      })

      return response.data
    }catch(e){
      return e
    }
  }

  async findAll({ page, pageSize, sort, name }) {

    let params = {
        page: page,
        page_size: pageSize,
        order: sort,
        name: name
    }

    let response = await Http.get('/user', { params} );
    return response.data.brands;
  }

  async create(user) {
    let response = await Http.post('/user/', user);
    return response.data;
  }

  async update(id, user, token) {
    try{
      const response = await Http.post(`/user/${id}`, user, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      
      return response
    }catch(e){
      return e
    }
  }

}

module.exports = Brand;