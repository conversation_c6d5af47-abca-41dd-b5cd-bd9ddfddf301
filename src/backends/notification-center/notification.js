const Http = require("./http-client")

class Notification {

  static getInstance() {
    if (!this.instance) 
      this.instance = new Notification();
    return this.instance;
  }

  async findById(id) {
    //let response = await Http.get('/ws/notifications/' + id);
    //return response.data;
    return {
      id: 123,
      message: "Sefaz SP está fora do ar.",
      date: "2019-06-27T18:04",
      userId: 1
    }
  }

  async findByUserId(userId) {
    //let response = await Http.get('/ws/notifications/' + userId);
    //return response.data;
    return [
      {
        id: 123,
        message: "Sefaz SP está fora do ar.",
        date: "2019-06-27T18:04",
        userId: 1
      },
      {
        id: 123,
        message: "Sefaz SP voltou",
        date: "2019-06-27T19:10",
        userId: 1
      }
    ];
  }
}

module.exports = Notification;