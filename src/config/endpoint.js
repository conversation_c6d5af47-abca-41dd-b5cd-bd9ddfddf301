module.exports =  {
    account: {
        baseURL: process.env.ACCOUNT_URL || "http://accounts"
    },
    roles: {
        baseURL: process.env.ROLES_URL
    },
    catalog: {
        baseURL: 'http://192.168.3.2/Desenv/Oliveira/Signa.roles.Api/',
        timeout: 10000,
        headers: {
            Authorization: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************.09eIhSl-g6hKyZEkBjSEOJoSRufbna2ML-SDozzdElQ'
        },
        auth: {
            username: 'admin',
            password: 'admin'
        }
    } 
}


