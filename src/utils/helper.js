
function MessageResolver (resolver)
{
 	if (resolver.data !== undefined)
		if (resolver.data.Message !== undefined)
			return resolver.data.Message
		else
			return resolver.data
	else
		if (resolver.response !== undefined)
			if (resolver.response.data !== undefined)
				if (resolver.response.data.Message !== undefined)
					throw resolver.response.data.Message
				else
					if (resolver.response.data != undefined && resolver.response.data !== '')
					{
						let response = JSON.parse(resolver.response.data)
						return response.Error.Text
					}
					else
						return resolver.message
			else
				return resolver.response.message
		else
			return resolver.message
}

function ResolveResponse (resolver)
{
	if (resolver.status !== undefined)
		return resolver
	if (resolver.response !== undefined)
		return resolver.response
 	if (resolver.code !== undefined)
		return { status : 400, message: resolver.message }
}
module.exports = { MessageResolver, ResolveResponse } 