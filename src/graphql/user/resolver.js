const { User } = require('../../backends/account')
const { Menu } = require('../../backends/menu')
const { serialize } = require('cookie')
const { MessageResolver, ResolveResponse } = require('../../utils/helper')

exports.resolver = {
  Query: {
    async me(parent, {}, context, info) {
      const currentUserId = 1 // TODO: get userId from token
      return await User.getInstance().findById(currentUserId)
    },

    async getUser(parent, { id, token }, context, info) {
      return await User.getInstance().findById(id, token)
    },

    async login(parent, { usuario, senha }, context, info) {
      const resolver = await User.getInstance().login({ usuario, senha })
      
      if (ResolveResponse(resolver).status !== 200) {
        throw MessageResolver(resolver)
      } else {
        const token = resolver.data.accessToken

        const cookie = serialize('SignaToken', token, { httpOnly: true, secure: true })
        context.res.setHeader('Set-Cookie', cookie)

        return resolver.data
      }
    },

    async logout(parent, { }, context, info) {

        context.res.setHeader('Set-Cookie', serialize('SignaToken', '', { httpOnly: true, secure: true, maxAge: -1 }))
        return true
    },

    async recovery(parent, { email, usuario }, context, info) {
        const resolver = await User.getInstance().recovery(email, usuario)
  
        if (ResolveResponse(resolver).status !== 200) {
          throw MessageResolver(resolver)
        } else {
          return resolver.data
        } 
    },

    async notification(parent, { id, token }, context, info) {
      const resolver = await User.getInstance().findNotificationById(id, token)
      return resolver
    },
  },

  Mutation: {
    async createUser(parent, { input }, context, info) {
      return await User.getInstance().create(input)
    },

    async updateUser(parent, { id, input, token }, context, info) {
      const resolver = await User.getInstance().update(id, input, token)

      if (ResolveResponse(resolver).status !== 200) {
        throw MessageResolver(resolver)
      } else {
        return resolver.data || resolver.message
      }
    },

    async recoveryChange(parent, { id, senha, token }, context, info) {
      const resolver = await User.getInstance().recoveryChange(id, senha, token)

      if (ResolveResponse(resolver).status !== 200) {
        throw MessageResolver(resolver)
      } else {
        return resolver.data
      }
    },

    async changePicture(parent, { id, picture, token }, context, info) {
      const resolver = await User.getInstance().changePicture(
        id,
        picture,
        token
      )
      
      if (ResolveResponse(resolver).status !== 200) {
        throw MessageResolver(resolver)
      } else {
        return resolver.data
      }
    },
  },

  User: {
    async menu(user) {
      if (!user.id) return null
      return await Menu.getInstance().findByUserId(user.id)
    },

    async favoriteMenus(user) {
      if (!user.id) return null
      return await Menu.getInstance().findFavoriteByUserId(user.id)
    },
  },
}
