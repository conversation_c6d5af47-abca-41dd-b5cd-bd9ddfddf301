type User {
  id: Int
  name: String
  email: String
  picture: String
  changePwd: Boolean
  isPwdExpired: Boolean
  allowChangePwd: Boolean
  menu: [Menu]
  favoriteMenus: [Menu]
}

type Me {
  id: Int,
  name: String
}

type Login {
  accessToken: String
  apiVersionModel: String
  logo: String
  refreshToken: String
  userId: Int
  errors: [Error]
}

type Logout {
  errors: [Error]
}

type Error {
  Message: String
}

type Recovery {
  message: String
  needUserName: Boolean
}

type Picture {
  message: String
}

input File {
  id: ID!
  path: String!
  filename: String!
  mimeType: String!
  encoding: String!
}

type Notification {
  message: String
}

input UserInput {
  name: String
  email: String
  password: String
}

type Query {
  # ### GET me
  # Get a specific user
  # _Arguments_ 
  # - **id**: User id
  me: User

  # ### GET user
  # Get a specific user
  # _Arguments_ 
  # - **id**: User id
  getUser(id: ID!, token: String): User 

  login(usuario: String, senha: String): Login

  logout: Logout

  recovery(email: String, usuario: String): Recovery

  notification(id: ID!, token: String): [Notification]
}

type Mutation {

  # ### Create a user
  # Create a new user
  # _Arguments_
  # - **input**: UserInput 
  createUser(input: UserInput!): User

  # ### Update user
  # Update a user
  # _Arguments_
  # - **id**: User id
  # - **input**: UserInput 
  updateUser(id: ID!, input: UserInput!, token: String): User

  recoveryChange(id: ID!, senha: String, token: String): Recovery

  changePicture(id: ID!, picture: File!, token: String): Picture!
}