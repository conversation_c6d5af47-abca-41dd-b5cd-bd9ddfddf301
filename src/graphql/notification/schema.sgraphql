type Notifications {
  id: Int
  message: String
  date: String
  userId: Int
  user: User
}

type Query {
  # ### GET notification 
  # Get a specific notification
  # _Arguments_ 
  # - **id**: Notification id
  getNotification(id: ID!): Notifications

  # ### GET notifications by user 
  # Get a list of notifications by userId
  # _Arguments_ 
  # - **userId**: Id of user 
  getNotificationByUser(id: ID!): [Notifications]  
}
