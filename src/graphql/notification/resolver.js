
// const { Notification } = require("../../backends/notification-center")
// const { User } = require("../../backends/account")

// exports.resolver = {
//     Query: {

//         async getNotification(parent, { id }, context, info) {
//           return await Notification.getInstance().findById(id);
//         },

//         async getNotificationByUser(parent, { userId }, context, info) {
//           return await Notification.getInstance().findByUserId(userId);
//         },
//     },

//     Notification: {
//       async user(notification) {
//         if (!notification.userId) return null;
//         return await User.getInstance().findById(notification.userId);
//       },
//     }
// }
