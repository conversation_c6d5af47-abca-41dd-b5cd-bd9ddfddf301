const { Menu } = require("../../backends/menu")

exports.resolver = {
    Query: {
        
        async getMenuByUserId(parent, { id, token }, context, info) {
          return await Menu.getInstance().findByUserId(id, token);
        },

        async getFavoriteMenusByUserId(parent, { id, token }, context, info) {
          return await Menu.getInstance().findFavoriteByUserId(id, token);
        },

        async getFunctionByName(parent, { name, token }, context, info) {
          return await Menu.getInstance().findFuncByName(name, token)
        },
        
        async getOptions(parent, info) {

            let response =
            {
              headerText: process.env.MENU_HEADER_TEXT,
              showLanguage: process.env.MENU_LANGUAGE_BUTTON === undefined ? true : process.env.MENU_LANGUAGE_BUTTON === 'true',
              showChat: process.env.MENU_CHAT_BUTTON === undefined ? true : process.env.MENU_CHAT_BUTTON === 'true'
            };

          return response;
        },
    },

    Mutation: {
 
    },

    Menu: {

    }
}
