type Menu {
  id: Int
  name: String
  icon: String
  href: String
  shortName: String
  fatherString: String
  meString: String
  children: [Menu] 
}

type Function {
  id: Int
  name: String
  shortName: String
  href: String
  ordemFuncao: Int
}

type Options {
  headerText: String
  showLanguage: Boolean
  showChat: Boolean
}

type Query {
  # ### GET menus
  # Get a list of Menu by userId
  # _Arguments_ 
  # - **id**: User id
  getMenuByUserId(id: ID!, token: String): [Menu]

  # ### GET favorite menus
  # Get a list of favorite menus by userId
  # _Arguments_ 
  # - **id**: User id
  getFavoriteMenusByUserId(id: ID!, token: String): [Menu]

  getFunctionByName(name: String, token: String): [Function]

  getOptions: Options
}
