#!/usr/bin/env node
const express = require("express");
const app = express();
const axios = require('axios')
const multer = require('multer')
const upload = multer({})
const FormData = require('form-data')
var server = require('./app');
const cors = require('cors')

/**
 * Get port from environment and store in Express.
 */

var port = process.env.PORT || '3000';

app.use(require('express-status-monitor')())
app.use(cors())
app.use(
  '/static/graphql-playground-react',
  express.static(require.resolve('graphql-playground-react/package.json').slice(0, -12)),
);

app.post('/upload', upload.single('picture'), async (req, res) => {
	//console.log(req.file)
	//console.log(req.body)
	let form = new FormData()
	form.append('picture', req.file)

	try{
		const res = await axios.post(`http://account.ap.signa.net.br/user/${req.body.id}/picture`, form, {
			headers: { Authorization: `Bearer ${req.body.token}` }
		})

		//console.log(res)
	}catch(e){
		console.log(e.response)
	}
})

server.applyMiddleware({ app });

app.listen({ port: port }, () =>
  console.log(`🚀 Server ready at http://localhost:${port}${server.graphqlPath}`)
)

/**
 * workaround for terminate process inside of container
 */
process.on('SIGINT', function() {
    process.exit();
});